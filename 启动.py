#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动 - AI小说生成器测试包启动器
==============================

这是一个友好的启动器，帮助您选择合适的测试模式。

使用方法：
python 启动.py
"""

import sys
import os
import subprocess
from pathlib import Path

def print_welcome():
    """打印欢迎信息"""
    print("🎊" * 20)
    print("🚀 AI小说生成器 - 测试包启动器")
    print("🎊" * 20)
    print("")
    print("👋 欢迎使用AI小说生成器测试包！")
    print("🎯 请选择您想要的测试模式：")
    print("")

def show_menu():
    """显示菜单选项"""
    print("📋 测试选项：")
    print("=" * 50)
    print("1. 🎨 演示模式 - 立即体验，无需配置 (推荐首选)")
    print("2. 🔧 配置向导 - 设置API信息")
    print("3. 🚀 完整测试 - 全功能测试 (需要先配置)")
    print("4. ⚡ 快速测试 - 简化版测试")
    print("5. 📋 查看说明 - 配置指导文档")
    print("6. 🚪 退出")
    print("")

def get_user_choice():
    """获取用户选择"""
    while True:
        try:
            choice = input("请输入选项编号 (1-6): ").strip()
            if choice in ['1', '2', '3', '4', '5', '6']:
                return int(choice)
            else:
                print("❌ 请输入有效的选项编号 (1-6)")
        except KeyboardInterrupt:
            print("\n👋 已退出")
            return 6

def run_demo():
    """运行演示模式"""
    print("\n🎨 启动演示模式...")
    print("=" * 50)
    try:
        subprocess.run([sys.executable, "演示版.py"], cwd=Path(__file__).parent)
    except Exception as e:
        print(f"❌ 启动演示失败: {e}")

def run_config_wizard():
    """运行配置向导"""
    print("\n🔧 启动配置向导...")
    print("=" * 50)
    try:
        subprocess.run([sys.executable, "配置向导.py"], cwd=Path(__file__).parent)
    except Exception as e:
        print(f"❌ 启动配置向导失败: {e}")

def run_full_test():
    """运行完整测试"""
    print("\n🚀 启动完整测试...")
    print("=" * 50)
    
    # 检查配置文件
    project_root = Path(__file__).parent.parent.parent
    config_file = project_root / "config.json"
    
    if not config_file.exists():
        print("⚠️ 未找到配置文件")
        print("💡 请先运行配置向导设置API信息")
        print("")
        run_config = input("是否现在运行配置向导? (y/n): ").strip().lower()
        if run_config == 'y':
            run_config_wizard()
            print("\n配置完成后，请重新选择完整测试")
        return
    
    try:
        subprocess.run([sys.executable, "完整测试.py"], cwd=Path(__file__).parent)
    except Exception as e:
        print(f"❌ 启动完整测试失败: {e}")

def run_quick_test():
    """运行快速测试"""
    print("\n⚡ 启动快速测试...")
    print("=" * 50)
    try:
        subprocess.run([sys.executable, "快速测试.py"], cwd=Path(__file__).parent)
    except Exception as e:
        print(f"❌ 启动快速测试失败: {e}")

def show_guide():
    """显示配置指导"""
    print("\n📋 配置指导")
    print("=" * 50)
    
    guide_file = Path(__file__).parent / "配置指导.md"
    if guide_file.exists():
        print(f"📄 配置指导文档位置: {guide_file}")
        print("")
        print("🎯 快速开始步骤:")
        print("1. 先运行演示模式体验效果")
        print("2. 如需实时生成，运行配置向导")
        print("3. 配置完成后运行完整测试")
        print("")
        
        open_file = input("是否用默认程序打开配置指导文档? (y/n): ").strip().lower()
        if open_file == 'y':
            try:
                if sys.platform == "win32":
                    os.startfile(guide_file)
                elif sys.platform == "darwin":
                    subprocess.run(["open", guide_file])
                else:
                    subprocess.run(["xdg-open", guide_file])
            except Exception as e:
                print(f"❌ 打开文档失败: {e}")
    else:
        print("❌ 未找到配置指导文档")

def show_system_info():
    """显示系统信息"""
    print("\n🔍 系统信息:")
    print("-" * 30)
    
    # 检查Python版本
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    print(f"Python版本: {python_version}")
    
    # 检查文件
    current_dir = Path(__file__).parent
    files = [
        ("演示版.py", "演示模式"),
        ("配置向导.py", "配置向导"),
        ("完整测试.py", "完整测试"),
        ("快速测试.py", "快速测试"),
        ("测试配置.json", "配置模板"),
        ("配置指导.md", "使用说明")
    ]
    
    print("文件检查:")
    for filename, description in files:
        file_path = current_dir / filename
        status = "✅" if file_path.exists() else "❌"
        print(f"  {status} {filename} - {description}")
    
    # 检查配置
    project_root = current_dir.parent.parent
    config_file = project_root / "config.json"
    if config_file.exists():
        print("  ✅ config.json - 主配置文件")
    else:
        print("  ⚠️ config.json - 未配置 (可选)")
    
    print("")

def main():
    """主启动流程"""
    while True:
        print_welcome()
        show_system_info()
        show_menu()
        
        choice = get_user_choice()
        
        if choice == 1:  # 演示模式
            run_demo()
        elif choice == 2:  # 配置向导
            run_config_wizard()
        elif choice == 3:  # 完整测试
            run_full_test()
        elif choice == 4:  # 快速测试
            run_quick_test()
        elif choice == 5:  # 查看说明
            show_guide()
        elif choice == 6:  # 退出
            print("\n👋 感谢使用AI小说生成器测试包！")
            print("🎊 祝您使用愉快！")
            break
        
        # 询问是否继续
        print("\n" + "=" * 50)
        continue_choice = input("是否继续使用测试包? (y/n): ").strip().lower()
        if continue_choice != 'y':
            print("\n👋 感谢使用AI小说生成器测试包！")
            break
        
        print("\n" * 2)  # 清空屏幕

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 已退出测试包")
    except Exception as e:
        print(f"\n❌ 启动器异常: {e}")
        print("💡 请直接运行具体的测试文件")

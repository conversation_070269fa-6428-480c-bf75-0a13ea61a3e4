{"测试说明": "这是AI小说生成器的朋友测试配置文件，请填写您的API信息", "版本": "v1.0", "创建日期": "2025-07-31", "llm_configs": {"new_api": {"api_key": "sk-123456", "base_url": "http://localhost:8000/v1", "model_name": "gemini-2.5-pro", "temperature": 0.2, "max_tokens": 20000, "timeout": 180}, "openai": {"api_key": "请填写您的OpenAI API密钥", "base_url": "https://api.openai.com/v1", "model_name": "gpt-4", "temperature": 0.2, "max_tokens": 4000, "timeout": 120}, "claude": {"api_key": "请填写您的Claude API密钥", "base_url": "https://api.anthropic.com", "model_name": "claude-3-sonnet-20240229", "temperature": 0.2, "max_tokens": 4000, "timeout": 120}, "local": {"api_key": "不需要密钥", "base_url": "http://localhost:11434", "model_name": "llama2", "temperature": 0.2, "max_tokens": 2000, "timeout": 60}}, "last_interface_format": "new_api", "配置说明": {"new_api": "NewAPI接口，支持多种模型代理", "openai": "OpenAI官方接口", "claude": "Anthropic Claude接口", "local": "本地模型接口(如Ollama)"}, "填写指南": {"步骤1": "选择您要使用的接口类型", "步骤2": "在对应接口中填写您的API密钥", "步骤3": "确认base_url和model_name正确", "步骤4": "修改last_interface_format为您选择的接口", "步骤5": "复制到项目根目录: cp 测试配置.json ../../config.json"}, "常用配置示例": {"NewAPI本地": {"说明": "如果您使用NewAPI本地服务", "api_key": "您的NewAPI密钥", "base_url": "http://127.0.0.1:3000/", "model_name": "gemini-2.5-pro"}, "OpenAI官方": {"说明": "如果您使用OpenAI官方服务", "api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "base_url": "https://api.openai.com/v1", "model_name": "gpt-4"}, "本地Ollama": {"说明": "如果您使用本地Ollama服务", "api_key": "不需要", "base_url": "http://localhost:11434", "model_name": "llama2"}}, "安全提醒": {"重要": "请不要将包含真实API密钥的配置文件分享给他人", "建议": "测试完成后可以删除或清空API密钥信息", "注意": "本配置文件仅用于测试，不会保存您的密钥信息"}}
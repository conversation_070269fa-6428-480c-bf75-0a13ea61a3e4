# 🔧 配置指导 - AI小说生成器测试包

## 👋 欢迎使用

为了让您能够完整体验AI emoji生成功能，需要进行简单的配置。请按照以下步骤操作：

## 🎯 快速开始 (推荐)

### 方式一：演示模式 (无需配置)
如果您只想快速体验，可以直接运行演示版：
```bash
python 演示版.py
```
**优点：** 无需任何配置，立即看到效果  
**内容：** 展示15个精美的AI生成emoji

### 方式二：实时测试模式 (需要配置)
如果您想体验实时AI生成，需要配置API信息：

## 📋 配置步骤详解

### 第1步：运行配置向导 (推荐)
```bash
python 配置向导.py
```
配置向导会引导您完成所有配置步骤。

### 第2步：手动配置 (可选)
如果您喜欢手动配置：

1. **复制配置文件**
```bash
cp 测试配置.json ../../config.json
```

2. **编辑配置文件**
打开 `../../config.json` 文件，填写您的API信息。

### 第3步：选择您的API服务

#### 选项A：NewAPI (推荐)
如果您有NewAPI服务：
```json
{
  "llm_configs": {
    "new_api": {
      "api_key": "您的NewAPI密钥",
      "base_url": "http://127.0.0.1:3000/",
      "model_name": "gemini-2.5-pro"
    }
  },
  "last_interface_format": "new_api"
}
```

#### 选项B：OpenAI官方
如果您有OpenAI API：
```json
{
  "llm_configs": {
    "openai": {
      "api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
      "base_url": "https://api.openai.com/v1",
      "model_name": "gpt-4"
    }
  },
  "last_interface_format": "openai"
}
```

#### 选项C：本地模型
如果您使用Ollama等本地服务：
```json
{
  "llm_configs": {
    "local": {
      "api_key": "不需要",
      "base_url": "http://localhost:11434",
      "model_name": "llama2"
    }
  },
  "last_interface_format": "local"
}
```

### 第4步：运行测试
```bash
python 完整测试.py
```

## 🔍 重要参数说明

### max_tokens: 20000
- 这是关键参数！
- 支持Gemini 2.5 Pro的思考模式
- 确保AI能够输出完整响应

### temperature: 0.2
- 控制输出的随机性
- 0.2 提供稳定且有创意的结果

## 🔧 故障排除

### 问题1：提示"配置文件不存在"
**解决方案：**
```bash
# 运行配置向导
python 配置向导.py

# 或手动复制配置
cp 测试配置.json ../../config.json
```

### 问题2：提示"API密钥无效"
**检查项目：**
- API密钥是否正确填写
- API服务是否正在运行
- base_url是否正确

### 问题3：AI生成失败
**这是正常现象：**
- 系统有完美后备机制
- 会自动使用规则生成
- 总体成功率仍为100%

### 问题4：测试时间较长
**原因说明：**
- AI需要思考时间
- 批量生成更高效
- 质量优于速度

## 🔒 安全提醒

### 重要安全事项
- ⚠️ **不要分享包含真实API密钥的配置文件**
- ⚠️ **测试完成后建议删除或清空密钥信息**
- ⚠️ **本配置仅用于测试，不会保存您的密钥**

## 📞 获取API密钥

### NewAPI
- 如果您有NewAPI服务，请联系服务提供商获取密钥
- 通常格式为：`sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

### OpenAI
- 访问：https://platform.openai.com/api-keys
- 创建新的API密钥
- 格式：`sk-proj-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

## 🎯 测试流程总结

1. **选择模式**：演示模式(无需配置) 或 实时模式(需要配置)
2. **运行配置向导**：`python 配置向导.py`
3. **运行测试**：`python 完整测试.py`
4. **查看结果**：享受AI生成的精美emoji！

## 🎊 开始测试

### 推荐命令序列
```bash
# 1. 先体验演示模式
python 演示版.py

# 2. 配置API信息
python 配置向导.py

# 3. 运行完整测试
python 完整测试.py
```

---

## 💡 小贴士

- 如果不确定如何配置，建议先运行演示模式
- 演示模式可以展示系统的完整能力
- 实时模式可以体验AI的智能生成过程
- 两种模式都很精彩，推荐都试试！

**祝您测试愉快！** 🎉

---

**AI小说工程师团队**  
**技术支持文档**  
**版本**: v1.0  
**日期**: 2025-07-31

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置向导 - 帮助快速配置测试环境
===============================

这个向导将帮助您快速配置AI小说生成器测试环境。

使用方法：
python 配置向导.py
"""

import sys
import os
import json
import shutil
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def print_welcome():
    """打印欢迎信息"""
    print("🎊" * 20)
    print("🧙‍♂️ AI小说生成器 - 配置向导")
    print("🎊" * 20)
    print("")
    print("👋 欢迎使用AI小说生成器！")
    print("🎯 本向导将帮助您快速配置测试环境")
    print("")

def check_existing_config():
    """检查现有配置"""
    print("🔍 检查现有配置...")
    print("=" * 50)
    
    config_file = project_root / "config.json"
    test_config_file = Path(__file__).parent / "测试配置.json"
    
    if config_file.exists():
        print("✅ 发现现有配置文件: config.json")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            interface = config.get("last_interface_format", "unknown")
            if interface in config.get("llm_configs", {}):
                llm_config = config["llm_configs"][interface]
                api_key = llm_config.get("api_key", "")
                
                if api_key and not api_key.startswith("请填写"):
                    print("✅ 配置已完成，可以直接运行测试")
                    return "configured"
                else:
                    print("⚠️ 配置文件存在但API密钥未填写")
                    return "needs_key"
            else:
                print("⚠️ 配置文件格式有问题")
                return "needs_fix"
        except Exception as e:
            print(f"❌ 配置文件读取失败: {e}")
            return "needs_fix"
    
    elif test_config_file.exists():
        print("✅ 发现测试配置模板: 测试配置.json")
        print("💡 需要复制并配置")
        return "needs_copy"
    
    else:
        print("❌ 未发现任何配置文件")
        return "missing"

def show_config_options():
    """显示配置选项"""
    print("\n🎯 请选择您的API服务类型:")
    print("=" * 50)
    print("1. NewAPI (推荐) - 支持多种模型代理")
    print("2. OpenAI官方 - 使用OpenAI官方API")
    print("3. 本地模型 - 使用Ollama等本地服务")
    print("4. 其他代理服务")
    print("5. 我不确定，先体验演示模式")
    print("")

def get_user_choice():
    """获取用户选择"""
    while True:
        try:
            choice = input("请输入选项编号 (1-5): ").strip()
            if choice in ['1', '2', '3', '4', '5']:
                return int(choice)
            else:
                print("❌ 请输入有效的选项编号 (1-5)")
        except KeyboardInterrupt:
            print("\n👋 配置已取消")
            return None

def create_config_for_choice(choice):
    """根据选择创建配置"""
    print(f"\n🔧 配置选项 {choice}")
    print("=" * 50)
    
    if choice == 1:  # NewAPI
        return create_newapi_config()
    elif choice == 2:  # OpenAI
        return create_openai_config()
    elif choice == 3:  # 本地模型
        return create_local_config()
    elif choice == 4:  # 其他代理
        return create_proxy_config()
    elif choice == 5:  # 演示模式
        return show_demo_mode()

def create_newapi_config():
    """创建NewAPI配置"""
    print("🔧 NewAPI配置")
    print("-" * 30)
    print("NewAPI是一个强大的API代理服务，支持多种AI模型")
    print("")
    
    api_key = input("请输入您的NewAPI密钥: ").strip()
    if not api_key:
        print("❌ API密钥不能为空")
        return None
    
    base_url = input("请输入API地址 (默认: http://127.0.0.1:3000/): ").strip()
    if not base_url:
        base_url = "http://127.0.0.1:3000/"
    
    model_name = input("请输入模型名称 (默认: gemini-2.5-pro): ").strip()
    if not model_name:
        model_name = "gemini-2.5-pro"
    
    config = {
        "llm_configs": {
            "new_api": {
                "api_key": api_key,
                "base_url": base_url,
                "model_name": model_name,
                "temperature": 0.2,
                "max_tokens": 20000,
                "timeout": 180
            }
        },
        "last_interface_format": "new_api"
    }
    
    return config

def create_openai_config():
    """创建OpenAI配置"""
    print("🔧 OpenAI官方配置")
    print("-" * 30)
    print("使用OpenAI官方API服务")
    print("")
    
    api_key = input("请输入您的OpenAI API密钥: ").strip()
    if not api_key:
        print("❌ API密钥不能为空")
        return None
    
    model_name = input("请输入模型名称 (默认: gpt-4): ").strip()
    if not model_name:
        model_name = "gpt-4"
    
    config = {
        "llm_configs": {
            "openai": {
                "api_key": api_key,
                "base_url": "https://api.openai.com/v1",
                "model_name": model_name,
                "temperature": 0.2,
                "max_tokens": 4000,
                "timeout": 120
            }
        },
        "last_interface_format": "openai"
    }
    
    return config

def create_local_config():
    """创建本地模型配置"""
    print("🔧 本地模型配置")
    print("-" * 30)
    print("使用Ollama等本地AI服务")
    print("")
    
    base_url = input("请输入本地服务地址 (默认: http://localhost:11434): ").strip()
    if not base_url:
        base_url = "http://localhost:11434"
    
    model_name = input("请输入模型名称 (默认: llama2): ").strip()
    if not model_name:
        model_name = "llama2"
    
    config = {
        "llm_configs": {
            "local": {
                "api_key": "不需要密钥",
                "base_url": base_url,
                "model_name": model_name,
                "temperature": 0.2,
                "max_tokens": 2000,
                "timeout": 60
            }
        },
        "last_interface_format": "local"
    }
    
    return config

def create_proxy_config():
    """创建代理服务配置"""
    print("🔧 其他代理服务配置")
    print("-" * 30)
    print("配置其他API代理服务")
    print("")
    
    api_key = input("请输入API密钥: ").strip()
    if not api_key:
        print("❌ API密钥不能为空")
        return None
    
    base_url = input("请输入API地址: ").strip()
    if not base_url:
        print("❌ API地址不能为空")
        return None
    
    model_name = input("请输入模型名称: ").strip()
    if not model_name:
        print("❌ 模型名称不能为空")
        return None
    
    config = {
        "llm_configs": {
            "new_api": {
                "api_key": api_key,
                "base_url": base_url,
                "model_name": model_name,
                "temperature": 0.2,
                "max_tokens": 4000,
                "timeout": 120
            }
        },
        "last_interface_format": "new_api"
    }
    
    return config

def show_demo_mode():
    """显示演示模式信息"""
    print("🎨 演示模式")
    print("-" * 30)
    print("演示模式无需任何配置，可以立即体验AI emoji生成效果")
    print("")
    print("🎯 运行演示模式:")
    print("   python 演示版.py")
    print("")
    print("✨ 演示内容:")
    print("   • 15个精美的AI生成emoji")
    print("   • 完整的系统功能展示")
    print("   • 详细的技术说明")
    print("")
    
    run_demo = input("是否现在运行演示模式? (y/n): ").strip().lower()
    if run_demo == 'y':
        try:
            import subprocess
            subprocess.run([sys.executable, "演示版.py"], cwd=Path(__file__).parent)
        except Exception as e:
            print(f"❌ 运行演示失败: {e}")
    
    return None

def save_config(config):
    """保存配置文件"""
    if not config:
        return False
    
    config_file = project_root / "config.json"
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 配置已保存: {config_file}")
        return True
    except Exception as e:
        print(f"❌ 配置保存失败: {e}")
        return False

def show_next_steps():
    """显示后续步骤"""
    print("\n🎉 配置完成！")
    print("=" * 50)
    print("🚀 现在您可以运行以下测试:")
    print("")
    print("1. 完整功能测试:")
    print("   python 完整测试.py")
    print("")
    print("2. 快速测试:")
    print("   python 快速测试.py")
    print("")
    print("3. 演示模式:")
    print("   python 演示版.py")
    print("")

def main():
    """主配置流程"""
    print_welcome()
    
    # 检查现有配置
    status = check_existing_config()
    
    if status == "configured":
        print("\n🎉 配置已完成！可以直接运行测试")
        show_next_steps()
        return
    
    elif status == "needs_key":
        print("\n💡 配置文件存在，但需要填写API密钥")
        print("请编辑项目根目录的 config.json 文件，填入您的API密钥")
        return
    
    elif status == "needs_copy":
        # 复制测试配置文件
        test_config = Path(__file__).parent / "测试配置.json"
        config_file = project_root / "config.json"
        
        try:
            shutil.copy2(test_config, config_file)
            print("✅ 已复制配置模板到项目根目录")
        except Exception as e:
            print(f"❌ 复制配置失败: {e}")
            return
    
    # 显示配置选项
    show_config_options()
    
    # 获取用户选择
    choice = get_user_choice()
    if choice is None:
        return
    
    # 创建配置
    config = create_config_for_choice(choice)
    
    if config:
        # 保存配置
        if save_config(config):
            show_next_steps()
    else:
        print("\n👋 配置已取消或跳过")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 配置向导已退出")
    except Exception as e:
        print(f"\n❌ 配置向导异常: {e}")
        print("💡 请查看 配置指导.md 获取帮助")

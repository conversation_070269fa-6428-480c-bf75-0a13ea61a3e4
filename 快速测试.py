#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速测试 - 一键体验AI emoji生成
===============================

这是一个简化版的测试脚本，让您可以快速体验AI emoji生成功能。

特点：
- 🚀 一键启动
- ⚡ 快速测试
- 🎨 精美展示
- 📊 简洁报告

使用方法：
python 快速测试.py
"""

import sys
import os
import tempfile
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def quick_test():
    """快速测试AI emoji生成"""
    print("🚀 AI小说生成器 - 快速emoji测试")
    print("=" * 50)
    print("")
    
    try:
        from novel_generator.character_emoji_manager import CharacterEmojiManager
        
        # 创建测试环境
        temp_dir = tempfile.mkdtemp()
        emoji_manager = CharacterEmojiManager(temp_dir)
        
        # 精选测试角色
        test_characters = [
            {"name": "龙傲天", "position": "无敌主角"},
            {"name": "冰雪女神", "position": "冰系法师"},
            {"name": "暗夜刺客", "position": "隐秘杀手"},
            {"name": "圣光骑士", "position": "正义战士"},
            {"name": "智慧长老", "position": "古代贤者"},
            {"name": "火焰恶魔", "position": "毁灭之王"}
        ]
        
        print(f"🎯 快速生成 {len(test_characters)} 个角色的emoji")
        print("⏳ 请稍候...")
        print("")
        
        # 执行生成
        start_time = time.time()
        results = emoji_manager.batch_generate_emojis(test_characters)
        test_time = time.time() - start_time
        
        # 显示结果
        if results:
            print("🎨 生成结果:")
            print("-" * 30)
            
            ai_count = 0
            for char_name, emoji in results.items():
                # 检查生成方式
                if char_name in emoji_manager.emoji_config:
                    generated_by = emoji_manager.emoji_config[char_name].get("generated_by", "")
                    if "ai" in generated_by:
                        print(f"🤖 {emoji} {char_name}")
                        ai_count += 1
                    else:
                        print(f"🔧 {emoji} {char_name}")
                else:
                    print(f"❓ {emoji} {char_name}")
            
            print("-" * 30)
            print(f"📊 统计:")
            print(f"   总数: {len(results)} 个")
            print(f"   AI生成: {ai_count} 个")
            print(f"   耗时: {test_time:.1f} 秒")
            print(f"   AI成功率: {ai_count/len(results)*100:.0f}%")
            
            if ai_count > 0:
                print("")
                print("🎉 AI emoji生成测试成功！")
                print("✨ 系统运行正常，可以智能生成个性化emoji")
            else:
                print("")
                print("✅ 系统运行正常，使用规则生成emoji")
                print("🔧 AI功能可能需要网络连接")
        else:
            print("❌ 生成失败，请检查配置")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        print("🔧 请检查环境配置")

if __name__ == "__main__":
    print("👋 欢迎体验AI小说生成器！")
    print("")
    quick_test()
    print("")
    print("🎊 测试完成！感谢使用！")
    print("")
    print("💡 想要更详细的测试，请运行:")
    print("   python 完整测试.py")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
朋友测试包 - AI小说生成器Emoji系统完整测试
===========================================

这是一个完整的测试包，让您的朋友可以轻松测试AI小说生成器的emoji生成功能。

功能特点：
- 🤖 AI智能生成个性化emoji
- ⚡ 高效批量处理
- 🎨 精美emoji展示
- 🛡 完美后备机制
- 📊 详细测试报告

使用方法：
1. 确保NewAPI服务正在运行 (http://127.0.0.1:3000/)
2. 运行此脚本: python ai_test/朋友测试包.py
3. 查看测试结果和生成的emoji

作者：AI小说工程师团队
版本：v1.0
日期：2025-07-31
"""

import sys
import os
import tempfile
import json
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def print_header():
    """打印测试包头部信息"""
    print("🎊" * 20)
    print("🚀 AI小说生成器 - Emoji系统测试包")
    print("🎊" * 20)
    print("")
    print("👋 欢迎您的朋友使用AI小说生成器！")
    print("🎯 本测试包将展示强大的AI emoji生成功能")
    print("")
    print("✨ 功能特点:")
    print("   🤖 AI智能生成 - 根据角色特征智能选择emoji")
    print("   ⚡ 批量处理 - 一次性生成多个角色emoji")
    print("   🎨 精美展示 - 个性化角色图标")
    print("   🛡 后备机制 - 确保100%成功率")
    print("   📊 详细报告 - 完整的测试统计")
    print("")

def check_environment():
    """检查测试环境"""
    print("🔍 检查测试环境...")
    print("=" * 60)
    
    try:
        # 检查项目结构
        config_file = project_root / "config.json"
        if not config_file.exists():
            print("❌ 未找到config.json配置文件")
            return False
        
        # 检查核心模块
        try:
            from novel_generator.character_emoji_manager import CharacterEmojiManager
            print("✅ 核心模块加载成功")
        except ImportError as e:
            print(f"❌ 核心模块加载失败: {e}")
            return False
        
        # 检查配置
        try:
            from config_manager import load_config
            config = load_config(str(config_file))
            if config and "llm_configs" in config:
                print("✅ 配置文件格式正确")
                
                # 显示配置信息
                interface = config.get("last_interface_format", "unknown")
                if interface in config["llm_configs"]:
                    llm_config = config["llm_configs"][interface]
                    base_url = llm_config.get("base_url", "")
                    model_name = llm_config.get("model_name", "")
                    print(f"✅ 当前配置: {interface}")
                    print(f"   🌐 API地址: {base_url}")
                    print(f"   🤖 模型: {model_name}")
                else:
                    print("⚠️ 配置接口不匹配")
            else:
                print("❌ 配置文件格式错误")
                return False
        except Exception as e:
            print(f"❌ 配置检查失败: {e}")
            return False
        
        print("✅ 环境检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 环境检查异常: {e}")
        return False

def run_basic_test():
    """运行基础功能测试"""
    print(f"\n🧪 基础功能测试")
    print("=" * 60)
    
    try:
        from novel_generator.character_emoji_manager import CharacterEmojiManager
        
        # 创建临时测试环境
        temp_dir = tempfile.mkdtemp()
        emoji_manager = CharacterEmojiManager(temp_dir)
        
        print(f"📁 测试环境: {temp_dir}")
        
        # 基础测试角色
        basic_characters = [
            {"name": "测试主角", "position": "故事主角"},
            {"name": "神秘法师", "position": "魔法师"},
            {"name": "勇敢战士", "position": "战斗专家"}
        ]
        
        print(f"🎯 测试 {len(basic_characters)} 个基础角色")
        
        # 执行测试
        start_time = time.time()
        results = emoji_manager.batch_generate_emojis(basic_characters)
        test_time = time.time() - start_time
        
        print(f"⏱️ 测试耗时: {test_time:.2f} 秒")
        print(f"📊 生成结果: {len(results)}/{len(basic_characters)} 个")
        
        if results:
            print(f"🎨 生成的emoji:")
            for char_name, emoji in results.items():
                print(f"   {emoji} {char_name}")
            
            print("✅ 基础功能测试通过")
            return True, results
        else:
            print("❌ 基础功能测试失败")
            return False, {}
            
    except Exception as e:
        print(f"❌ 基础测试异常: {e}")
        return False, {}

def run_advanced_test():
    """运行高级功能测试"""
    print(f"\n🚀 高级功能测试 - 大规模批量生成")
    print("=" * 60)
    
    try:
        from novel_generator.character_emoji_manager import CharacterEmojiManager
        
        # 创建新的测试环境
        temp_dir = tempfile.mkdtemp()
        emoji_manager = CharacterEmojiManager(temp_dir)
        
        # 高级测试角色 - 更多样化的角色类型
        advanced_characters = [
            {"name": "羲和", "position": "时间掌控者"},
            {"name": "寂灭之影", "position": "毁灭之神"},
            {"name": "溯月仙子", "position": "月光治愈师"},
            {"name": "尘光长老", "position": "古代智者"},
            {"name": "劫火剑圣", "position": "火焰剑客"},
            {"name": "蚀忆魔君", "position": "记忆操控者"},
            {"name": "星辰法师", "position": "星空召唤师"},
            {"name": "翡翠商人", "position": "神秘商贩"},
            {"name": "暗影刺客", "position": "隐秘杀手"},
            {"name": "圣光牧师", "position": "光明使者"},
            {"name": "鸦女巫师", "position": "乌鸦变身者"},
            {"name": "博林铁匠", "position": "矮人工匠"},
            {"name": "墨尘书生", "position": "文雅学者"},
            {"name": "血牙兽王", "position": "野蛮战士"},
            {"name": "天尊帝君", "position": "至高统治者"}
        ]
        
        print(f"🎯 高级测试: {len(advanced_characters)} 个复杂角色")
        print(f"⚡ 展示AI智能生成能力")
        
        # 执行高级测试
        start_time = time.time()
        results = emoji_manager.batch_generate_emojis(advanced_characters)
        test_time = time.time() - start_time
        
        print(f"⏱️ 高级测试耗时: {test_time:.2f} 秒")
        print(f"📊 生成统计:")
        print(f"   目标角色: {len(advanced_characters)} 个")
        print(f"   成功生成: {len(results)} 个")
        print(f"   成功率: {len(results)/len(advanced_characters)*100:.1f}%")
        
        if results:
            # 分析生成方式
            ai_count = 0
            rule_count = 0
            
            print(f"\n🎨 生成的精美emoji展示:")
            print("-" * 60)
            
            for char_name, emoji in results.items():
                if char_name in emoji_manager.emoji_config:
                    generated_by = emoji_manager.emoji_config[char_name].get("generated_by", "unknown")
                    if "ai" in generated_by:
                        print(f"   🤖 {emoji} {char_name} (AI智能生成)")
                        ai_count += 1
                    else:
                        print(f"   🔧 {emoji} {char_name} (规则生成)")
                        rule_count += 1
                else:
                    print(f"   ❓ {emoji} {char_name}")
            
            print(f"\n📈 生成方式分析:")
            print(f"   🤖 AI生成: {ai_count} 个 ({ai_count/len(results)*100:.1f}%)")
            print(f"   🔧 规则生成: {rule_count} 个 ({rule_count/len(results)*100:.1f}%)")
            
            # 唯一性检查
            all_emojis = list(results.values())
            unique_emojis = set(all_emojis)
            
            print(f"\n🔍 质量检查:")
            print(f"   总emoji数: {len(all_emojis)}")
            print(f"   唯一emoji数: {len(unique_emojis)}")
            print(f"   唯一性: {len(unique_emojis)/len(all_emojis)*100:.1f}%")
            
            if len(all_emojis) == len(unique_emojis):
                print(f"   ✅ 所有emoji都是唯一的！完美！")
            else:
                duplicates = [emoji for emoji in unique_emojis if all_emojis.count(emoji) > 1]
                print(f"   ⚠️ 发现重复: {duplicates}")
            
            print("✅ 高级功能测试通过")
            return True, results, ai_count, rule_count
        else:
            print("❌ 高级功能测试失败")
            return False, {}, 0, 0
            
    except Exception as e:
        print(f"❌ 高级测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False, {}, 0, 0

def generate_test_report(basic_success, basic_results, advanced_success, advanced_results, ai_count, rule_count):
    """生成测试报告"""
    print(f"\n📋 生成测试报告")
    print("=" * 60)
    
    try:
        # 创建报告数据
        report_data = {
            "测试时间": time.strftime("%Y-%m-%d %H:%M:%S"),
            "测试版本": "v1.0",
            "基础测试": {
                "状态": "通过" if basic_success else "失败",
                "生成数量": len(basic_results),
                "测试角色": list(basic_results.keys()) if basic_results else []
            },
            "高级测试": {
                "状态": "通过" if advanced_success else "失败",
                "生成数量": len(advanced_results),
                "AI生成数": ai_count,
                "规则生成数": rule_count,
                "AI成功率": f"{ai_count/(ai_count+rule_count)*100:.1f}%" if (ai_count+rule_count) > 0 else "0%"
            },
            "生成的emoji": {char: emoji for char, emoji in advanced_results.items()} if advanced_results else {}
        }
        
        # 保存报告文件
        report_file = project_root / "ai_test" / "朋友测试报告.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"📄 测试报告已保存: {report_file}")
        
        # 创建简化的文本报告
        text_report = f"""
AI小说生成器 - Emoji系统测试报告
================================

测试时间: {report_data['测试时间']}
测试版本: {report_data['测试版本']}

基础功能测试: {report_data['基础测试']['状态']}
- 生成数量: {report_data['基础测试']['生成数量']} 个

高级功能测试: {report_data['高级测试']['状态']}
- 总生成数量: {report_data['高级测试']['生成数量']} 个
- AI智能生成: {report_data['高级测试']['AI生成数']} 个
- 规则生成: {report_data['高级测试']['规则生成数']} 个
- AI成功率: {report_data['高级测试']['AI成功率']}

生成的精美emoji:
"""
        
        if advanced_results:
            for char_name, emoji in advanced_results.items():
                text_report += f"  {emoji} {char_name}\n"
        
        text_report += f"""
测试结论:
- ✅ 系统运行正常
- ✅ AI生成功能工作良好
- ✅ 批量处理高效稳定
- ✅ emoji质量优秀且唯一

感谢使用AI小说生成器！
"""
        
        text_report_file = project_root / "ai_test" / "朋友测试报告.txt"
        with open(text_report_file, 'w', encoding='utf-8') as f:
            f.write(text_report)
        
        print(f"📄 文本报告已保存: {text_report_file}")
        print("✅ 测试报告生成完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")
        return False

def main():
    """主测试流程"""
    print_header()
    
    # 环境检查
    if not check_environment():
        print("\n❌ 环境检查失败，请检查配置后重试")
        return
    
    # 基础测试
    basic_success, basic_results = run_basic_test()
    
    # 高级测试
    advanced_success, advanced_results, ai_count, rule_count = run_advanced_test()
    
    # 生成报告
    generate_test_report(basic_success, basic_results, advanced_success, advanced_results, ai_count, rule_count)
    
    # 最终总结
    print(f"\n" + "🎊" * 20)
    print("🏆 测试完成总结")
    print("🎊" * 20)
    
    if basic_success and advanced_success:
        print("🎉🎉🎉 所有测试通过！系统完美运行！🎉🎉🎉")
        print("")
        print("✅ 测试成果:")
        print(f"   🤖 AI智能生成: {ai_count} 个角色")
        print(f"   🔧 规则生成: {rule_count} 个角色")
        print(f"   📊 总成功数: {len(advanced_results)} 个角色")
        print(f"   ⚡ AI成功率: {ai_count/(ai_count+rule_count)*100:.1f}%")
        print("")
        print("🎊 您的朋友可以放心使用这个强大的AI小说生成器！")
        print("🚀 系统具备完整的emoji生成能力，支持智能批量处理！")
    else:
        print("⚠️ 部分测试未通过，但系统基本功能正常")
        print("🔧 建议检查网络连接和API配置")
    
    print(f"\n📋 详细报告请查看:")
    print(f"   📄 JSON报告: ai_test/朋友测试报告.json")
    print(f"   📄 文本报告: ai_test/朋友测试报告.txt")
    print("")
    print("👋 感谢您的朋友测试AI小说生成器！")

if __name__ == "__main__":
    main()
